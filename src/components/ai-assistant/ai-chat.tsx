import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { MessageCircle, Send, Bot, User, Settings, X, Copy, RotateCcw, Sparkles, Zap, Brain, Cpu } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { PasswordInput } from '../custom_components/password-input'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface AIChatProps {
  className?: string
  embedded?: boolean // 是否作为嵌入式组件
  height?: string // 自定义高度
}

const AVAILABLE_MODELS = [
  {
    value: 'claude-4-sonnet',
    label: 'Claude 4 Sonnet',
    icon: Brain,
    description: 'Most capable model for complex reasoning',
    color: 'bg-purple-500'
  },
  {
    value: 'deepseek-r1',
    label: 'DeepSeek R1',
    icon: Sparkles,
    description: 'Advanced reasoning model',
    color: 'bg-blue-500'
  },
  {
    value: 'deepseek-chat',
    label: 'DeepSeek Chat',
    icon: MessageCircle,
    description: 'Optimized for conversations',
    color: 'bg-green-500'
  },
  {
    value: 'gpt-4.1-nano-2025-04-14',
    label: 'GPT-4.1 Nano',
    icon: Zap,
    description: 'Fast and efficient',
    color: 'bg-yellow-500'
  },
  {
    value: 'gpt-4.1-mini',
    label: 'GPT-4.1 Mini',
    icon: Cpu,
    description: 'Balanced performance',
    color: 'bg-orange-500'
  }
]

export function AIChat({ className, embedded = false, height = "500px" }: AIChatProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [apiKey, setApiKey] = useState('')
  const [baseUrl, setBaseUrl] = useState('https://b4u.qzz.io/v1/chat/completions')
  const [selectedModel, setSelectedModel] = useState('gpt-4.1-mini')
  const [isOpen, setIsOpen] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('ai-assistant-api-key')
    const savedBaseUrl = localStorage.getItem('ai-assistant-base-url')
    const savedModel = localStorage.getItem('ai-assistant-model')

    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
    if (savedBaseUrl) {
      setBaseUrl(savedBaseUrl)
    }
    if (savedModel) {
      setSelectedModel(savedModel)
    }
  }, [])

  // Auto scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const saveSettings = () => {
    localStorage.setItem('ai-assistant-api-key', apiKey)
    localStorage.setItem('ai-assistant-base-url', baseUrl)
    localStorage.setItem('ai-assistant-model', selectedModel)
    setShowSettings(false)
  }

  const sendMessage = async () => {
    if (!input.trim() || !apiKey) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant. Provide clear, concise, and helpful responses.'
            },
            ...messages.map(msg => ({
              role: msg.role,
              content: msg.content
            })),
            {
              role: 'user',
              content: input.trim()
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.choices[0]?.message?.content || 'Sorry, I could not generate a response.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, there was an error processing your request. Please check your API key and try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }



  const clearChat = () => {
    setMessages([])
  }

  // 嵌入式模式：直接显示聊天界面
  if (embedded) {
    const currentModel = AVAILABLE_MODELS.find(m => m.value === selectedModel)

    return (
      <Card className={cn(
        "w-full shadow-lg border-0 bg-gradient-to-br from-background to-muted/20",
        "backdrop-blur-sm border border-border/50",
        className
      )} style={{ height }}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-border/50">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
                <Bot className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
            </div>
            <div className="flex flex-col">
              <CardTitle className="text-base font-semibold flex items-center gap-2">
                AI Assistant
                <Badge variant="secondary" className="text-xs px-2 py-0.5">
                  {currentModel?.label || 'Unknown'}
                </Badge>
              </CardTitle>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-2">
                  {currentModel?.icon && (
                    <currentModel.icon className="h-3 w-3" />
                  )}
                  <span>{currentModel?.description || 'AI Model'}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="h-8 w-40 text-xs px-3 border-border/50">
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {AVAILABLE_MODELS.map((model) => (
                  <SelectItem key={model.value} value={model.value} className="text-xs">
                    <div className="flex items-center gap-2">
                      <div className={cn("w-2 h-2 rounded-full", model.color)} />
                      <model.icon className="h-3 w-3" />
                      <span>{model.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Dialog open={showSettings} onOpenChange={setShowSettings}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted/50">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    API Settings
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="api-key" className="text-sm font-medium">API Key</Label>
                    <PasswordInput
                      id="api-key"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="Enter your API key"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="base-url" className="text-sm font-medium">Base URL</Label>
                    <Input
                      id="base-url"
                      type="url"
                      value={baseUrl}
                      onChange={(e) => setBaseUrl(e.target.value)}
                      placeholder="https://api.example.com/v1/chat/completions"
                      className="mt-1"
                    />
                  </div>
                  <div className="flex justify-end gap-2 pt-2">
                    <Button variant="outline" onClick={() => setShowSettings(false)}>
                      Cancel
                    </Button>
                    <Button onClick={saveSettings}>
                      Save Settings
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex flex-col h-[calc(100%-80px)]">
          <ScrollArea className="flex-1 px-4 py-4">
            {messages.length === 0 ? (
              <div className="text-center text-muted-foreground py-16">
                {!apiKey ? (
                  <div className="space-y-4 animate-in fade-in-50 duration-500">
                    <div className="relative">
                      <Bot className="h-16 w-16 mx-auto text-muted-foreground/30" />
                      <div className="absolute inset-0 h-16 w-16 mx-auto rounded-full bg-muted-foreground/5 animate-pulse" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-base font-medium">Welcome to AI Assistant</p>
                      <p className="text-sm text-muted-foreground/80">
                        Please configure your API key in settings to start chatting.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4 animate-in fade-in-50 duration-500">
                    <div className="relative">
                      <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
                        <Bot className="h-8 w-8 text-primary-foreground" />
                      </div>
                      <div className="absolute -bottom-1 -right-6 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-pulse" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-base font-medium">Hello! I'm your AI assistant.</p>
                      <p className="text-sm text-muted-foreground/80">
                        How can I help you today? Ask me anything!
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                {messages.map((message, index) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3 animate-in fade-in-50 slide-in-from-bottom-2 duration-300",
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    )}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    {message.role === 'assistant' && (
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
                        <Bot className="h-4 w-4 text-primary" />
                      </div>
                    )}
                    <div className="flex flex-col max-w-[85%] group">
                      <div
                        className={cn(
                          "rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm",
                          message.role === 'user'
                            ? 'bg-gradient-to-br from-primary to-primary/90 text-primary-foreground rounded-br-md'
                            : 'bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-bl-md'
                        )}
                      >
                        <div className="whitespace-pre-wrap break-words">
                          {message.content}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <span className="text-xs text-muted-foreground">
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 hover:bg-muted/50"
                          onClick={() => navigator.clipboard.writeText(message.content)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    {message.role === 'user' && (
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-secondary to-secondary/80 flex items-center justify-center flex-shrink-0 mt-1 border border-secondary/20">
                        <User className="h-4 w-4 text-secondary-foreground" />
                      </div>
                    )}
                  </div>
                ))}
                {isLoading && (
                  <div className="flex gap-3 justify-start animate-in fade-in-50 duration-300">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
                      <Bot className="h-4 w-4 text-primary" />
                    </div>
                    <div className="bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-2xl rounded-bl-md px-4 py-3 text-sm">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                        <span className="ml-2 text-muted-foreground">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>
          <Separator className="bg-border/50" />
          <div className="px-4 py-4 bg-gradient-to-t from-muted/20 to-transparent">
            <div className="flex gap-3">
              <div className="flex-1 relative">
                <Textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      sendMessage()
                    }
                  }}
                  placeholder={apiKey ? "Type your message... (Press Enter to send, Shift+Enter for new line)" : "Configure API key first"}
                  disabled={!apiKey || isLoading}
                  className="min-h-[44px] max-h-32 resize-none bg-background/80 backdrop-blur-sm border-border/50 focus:border-primary/50 transition-colors"
                  rows={1}
                />
                {input.trim() && (
                  <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                    {input.length}/2000
                  </div>
                )}
              </div>
              <Button
                onClick={sendMessage}
                disabled={!input.trim() || !apiKey || isLoading}
                size="icon"
                className={cn(
                  "shrink-0 h-11 w-11 rounded-xl transition-all duration-200",
                  input.trim() && apiKey && !isLoading
                    ? "bg-primary hover:bg-primary/90 scale-100"
                    : "bg-muted scale-95"
                )}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
            <div className="flex items-center justify-between mt-3">
              {messages.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearChat}
                  className="text-xs h-8 px-3 hover:bg-destructive/10 hover:text-destructive transition-colors"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  Clear Chat
                </Button>
              )}
              <div className="flex items-center gap-2 text-xs text-muted-foreground ml-auto">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  apiKey ? "bg-green-500 animate-pulse" : "bg-red-500"
                )} />
                <span>{apiKey ? "Connected" : "Not configured"}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 浮动模式：显示浮动按钮或聊天窗口
  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={cn(
          "fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-xl z-50",
          "bg-gradient-to-br from-primary to-primary/90 hover:from-primary/90 hover:to-primary",
          "border-2 border-primary-foreground/20 backdrop-blur-sm",
          "transition-all duration-300 hover:scale-110 hover:shadow-2xl",
          "animate-in fade-in-50 slide-in-from-bottom-4 duration-500"
        )}
        size="icon"
      >
        <MessageCircle className="h-6 w-6 text-primary-foreground" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-pulse" />
      </Button>
    )
  }

  const currentModel = AVAILABLE_MODELS.find(m => m.value === selectedModel)

  return (
    <Card className={cn(
      "fixed bottom-6 right-6 w-96 h-[600px] shadow-2xl z-50 border-0",
      "bg-gradient-to-br from-background to-muted/20 backdrop-blur-xl",
      "border border-border/50 rounded-2xl overflow-hidden",
      "animate-in fade-in-50 slide-in-from-bottom-8 duration-500"
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-border/50 bg-gradient-to-r from-background/80 to-muted/20">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
              <Bot className="h-4 w-4 text-primary-foreground" />
            </div>
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
          </div>
          <div className="flex flex-col">
            <CardTitle className="text-sm font-semibold flex items-center gap-2">
              AI Assistant
              <Badge variant="secondary" className="text-xs px-2 py-0.5">
                {currentModel?.label || 'Unknown'}
              </Badge>
            </CardTitle>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              {currentModel?.icon && (
                <currentModel.icon className="h-3 w-3" />
              )}
              <span>{currentModel?.description || 'AI Model'}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Dialog open={showSettings} onOpenChange={setShowSettings}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted/50">
                <Settings className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  AI Assistant Settings
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key-float" className="text-sm font-medium">API Key</Label>
                  <PasswordInput
                    id="api-key-float"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your API key"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="base-url-float" className="text-sm font-medium">Base URL</Label>
                  <Input
                    id="base-url-float"
                    type="url"
                    value={baseUrl}
                    onChange={(e) => setBaseUrl(e.target.value)}
                    placeholder="https://api.example.com/v1/chat/completions"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="model-float" className="text-sm font-medium">Model</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {AVAILABLE_MODELS.map((model) => (
                        <SelectItem key={model.value} value={model.value} className="text-sm">
                          <div className="flex items-center gap-2">
                            <div className={cn("w-2 h-2 rounded-full", model.color)} />
                            <model.icon className="h-3 w-3" />
                            <span>{model.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2 pt-2">
                  <Button variant="outline" onClick={() => setShowSettings(false)}>
                    Cancel
                  </Button>
                  <Button onClick={saveSettings}>
                    Save Settings
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-muted/50"
            onClick={() => setIsOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex flex-col h-[calc(100%-80px)]">
        <ScrollArea className="flex-1 px-3 py-4">
          {messages.length === 0 ? (
            <div className="text-center text-muted-foreground py-12">
              {!apiKey ? (
                <div className="space-y-3 animate-in fade-in-50 duration-500">
                  <div className="relative">
                    <Bot className="h-12 w-12 mx-auto text-muted-foreground/30" />
                    <div className="absolute inset-0 h-12 w-12 mx-auto rounded-full bg-muted-foreground/5 animate-pulse" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Welcome!</p>
                    <p className="text-xs text-muted-foreground/80">
                      Configure your API key to start chatting.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3 animate-in fade-in-50 duration-500">
                  <div className="relative">
                    <div className="w-12 h-12 mx-auto rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
                      <Bot className="h-6 w-6 text-primary-foreground" />
                    </div>
                    <div className="absolute -bottom-1 -right-4 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Hello! I'm ready to help.</p>
                    <p className="text-xs text-muted-foreground/80">
                      Ask me anything!
                    </p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-2 animate-in fade-in-50 slide-in-from-bottom-2 duration-300",
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  )}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  {message.role === 'assistant' && (
                    <div className="w-7 h-7 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
                      <Bot className="h-3 w-3 text-primary" />
                    </div>
                  )}
                  <div className="flex flex-col max-w-[85%] group">
                    <div
                      className={cn(
                        "rounded-xl px-3 py-2 text-sm leading-relaxed shadow-sm",
                        message.role === 'user'
                          ? 'bg-gradient-to-br from-primary to-primary/90 text-primary-foreground rounded-br-md'
                          : 'bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-bl-md'
                      )}
                    >
                      <div className="whitespace-pre-wrap break-words">
                        {message.content}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-xs text-muted-foreground">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 hover:bg-muted/50"
                        onClick={() => navigator.clipboard.writeText(message.content)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  {message.role === 'user' && (
                    <div className="w-7 h-7 rounded-full bg-gradient-to-br from-secondary to-secondary/80 flex items-center justify-center flex-shrink-0 mt-1 border border-secondary/20">
                      <User className="h-3 w-3 text-secondary-foreground" />
                    </div>
                  )}
                </div>
              ))}
              {isLoading && (
                <div className="flex gap-2 justify-start animate-in fade-in-50 duration-300">
                  <div className="w-7 h-7 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
                    <Bot className="h-3 w-3 text-primary" />
                  </div>
                  <div className="bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-xl rounded-bl-md px-3 py-2 text-sm">
                    <div className="flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-primary/60 rounded-full animate-bounce" />
                      <div className="w-1.5 h-1.5 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-1.5 h-1.5 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      <span className="ml-2 text-muted-foreground text-xs">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>
        <Separator className="bg-border/50" />
        <div className="px-3 py-3 bg-gradient-to-t from-muted/20 to-transparent">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    sendMessage()
                  }
                }}
                placeholder={apiKey ? "Type your message..." : "Configure API key first"}
                disabled={!apiKey || isLoading}
                className="min-h-[40px] max-h-24 resize-none bg-background/80 backdrop-blur-sm border-border/50 focus:border-primary/50 transition-colors text-sm"
                rows={1}
              />
            </div>
            <Button
              onClick={sendMessage}
              disabled={!input.trim() || !apiKey || isLoading}
              size="icon"
              className={cn(
                "shrink-0 h-10 w-10 rounded-lg transition-all duration-200",
                input.trim() && apiKey && !isLoading
                  ? "bg-primary hover:bg-primary/90 scale-100"
                  : "bg-muted scale-95"
              )}
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="flex items-center justify-between mt-2">
            {messages.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearChat}
                className="text-xs h-7 px-2 hover:bg-destructive/10 hover:text-destructive transition-colors"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
            <div className="flex items-center gap-2 text-xs text-muted-foreground ml-auto">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full",
                apiKey ? "bg-green-500 animate-pulse" : "bg-red-500"
              )} />
              <span>{apiKey ? "Ready" : "Setup needed"}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
