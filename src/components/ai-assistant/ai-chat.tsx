import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { MessageCircle, Send, Bot, User, Settings, X, Copy, RotateCcw, Sparkles, Zap, Brain, Cpu } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { PasswordInput } from '../custom_components/password-input'
import { cn } from '@/lib/utils'


interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface AIChatProps {
  className?: string
  embedded?: boolean // 是否作为嵌入式组件
  height?: string // 自定义高度
}

// 提取的子组件接口
interface ChatComponentProps {
  messages: Message[]
  isLoading: boolean
  apiKey?: string
  messagesEndRef: React.RefObject<HTMLDivElement | null>
  isEmbedded?: boolean
}

interface ChatInputProps {
  input: string
  setInput: (value: string) => void
  isLoading: boolean
  apiKey: string
  onSend: () => void
  isEmbedded?: boolean
}

interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  apiKey: string
  setApiKey: (key: string) => void
  baseUrl: string
  setBaseUrl: (url: string) => void
  onSave: () => void
}

interface ChatHeaderProps {
  currentModel: any
  selectedModel: string
  setSelectedModel: (model: string) => void
  onSettingsClick: () => void
  onClose?: () => void
  isEmbedded?: boolean
}

// 空状态组件
const EmptyState: React.FC<{ apiKey: string; isEmbedded?: boolean }> = ({ apiKey, isEmbedded }) => {
  const padding = isEmbedded ? "py-16" : "py-12"

  return (
    <div className={`text-center text-muted-foreground ${padding}`}>
      {!apiKey ? (
        <div className="space-y-4 animate-in fade-in-50 duration-500">
          <div className="relative">
            <Bot className="h-16 w-16 mx-auto text-muted-foreground/30" />
            <div className="absolute inset-0 h-16 w-16 mx-auto rounded-full bg-muted-foreground/5 animate-pulse" />
          </div>
          <div className="space-y-2">
            <p className="text-base font-medium">Welcome to AI Assistant</p>
            <p className="text-sm text-muted-foreground/80">
              Please configure your API key in settings to start chatting.
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-4 animate-in fade-in-50 duration-500">
          <div className="relative">
            <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
              <Bot className="h-8 w-8 text-primary-foreground" />
            </div>
            <div className="absolute -bottom-1 -right-6 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-pulse" />
          </div>
          <div className="space-y-2">
            <p className="text-base font-medium">Hello! I'm your AI assistant.</p>
            <p className="text-sm text-muted-foreground/80">
              How can I help you today? Ask me anything!
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

// 消息列表组件
const MessageList: React.FC<ChatComponentProps> = ({ messages, isLoading, messagesEndRef, isEmbedded }) => {
  const spacing = isEmbedded ? "space-y-6" : "space-y-4"
  const gap = isEmbedded ? "gap-3" : "gap-2"

  return (
    <div className={spacing}>
      {messages.map((message, index) => (
        <div
          key={message.id}
          className={cn(
            `flex ${gap} animate-in fade-in-50 slide-in-from-bottom-2 duration-300`,
            message.role === 'user' ? 'justify-end' : 'justify-start'
          )}
          style={{ animationDelay: `${index * 50}ms` }}
        >
          {message.role === 'assistant' && (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
              <Bot className="h-4 w-4 text-primary" />
            </div>
          )}
          <div className="flex flex-col max-w-[85%] group">
            <div
              className={cn(
                "rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm",
                message.role === 'user'
                  ? 'bg-gradient-to-br from-primary to-primary/90 text-primary-foreground rounded-br-md'
                  : 'bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-bl-md'
              )}
            >
              <div className="whitespace-pre-wrap break-words">
                {message.content}
              </div>
            </div>
            <div className="flex items-center gap-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="text-xs text-muted-foreground">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 hover:bg-muted/50"
                onClick={() => navigator.clipboard.writeText(message.content)}
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
          {message.role === 'user' && (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-secondary to-secondary/80 flex items-center justify-center flex-shrink-0 mt-1 border border-secondary/20">
              <User className="h-4 w-4 text-secondary-foreground" />
            </div>
          )}
        </div>
      ))}
      {isLoading && (
        <div className={`flex ${gap} justify-start animate-in fade-in-50 duration-300`}>
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center flex-shrink-0 mt-1 border border-primary/20">
            <Bot className="h-4 w-4 text-primary" />
          </div>
          <div className="bg-gradient-to-br from-muted to-muted/80 border border-border/50 rounded-2xl rounded-bl-md px-4 py-3 text-sm">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
              <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              <span className="ml-2 text-muted-foreground">AI is thinking...</span>
            </div>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  )
}

const AVAILABLE_MODELS = [
  {
    value: 'claude-4-sonnet',
    label: 'Claude 4 Sonnet',
    icon: Brain,
    description: 'Most capable model for complex reasoning',
    color: 'bg-purple-500'
  },
  {
    value: 'deepseek-r1',
    label: 'DeepSeek R1',
    icon: Sparkles,
    description: 'Advanced reasoning model',
    color: 'bg-blue-500'
  },
  {
    value: 'deepseek-chat',
    label: 'DeepSeek Chat',
    icon: MessageCircle,
    description: 'Optimized for conversations',
    color: 'bg-green-500'
  },
  {
    value: 'gpt-4.1-nano-2025-04-14',
    label: 'GPT-4.1 Nano',
    icon: Zap,
    description: 'Fast and efficient',
    color: 'bg-yellow-500'
  },
  {
    value: 'gpt-4.1-mini',
    label: 'GPT-4.1 Mini',
    icon: Cpu,
    description: 'Balanced performance',
    color: 'bg-orange-500'
  }
]

// 状态指示器组件
const StatusIndicator: React.FC<{ apiKey: string; isEmbedded?: boolean }> = ({ apiKey, isEmbedded }) => {
  const statusText = isEmbedded
    ? (apiKey ? "Configuraed" : "Not configured")
    : (apiKey ? "Ready" : "Setup needed")

  return (
    <div className="flex items-center gap-2 text-xs text-muted-foreground">
      <div className={cn(
        "w-2 h-2 rounded-full",
        apiKey ? "bg-green-500 animate-pulse" : "bg-red-500"
      )} />
      <span>{statusText}</span>
    </div>
  )
}

// 输入组件
const ChatInput: React.FC<ChatInputProps> = ({ input, setInput, isLoading, apiKey, onSend, isEmbedded }) => {
  const padding = isEmbedded ? "px-4 py-4" : "px-3 py-3"
  const gap = isEmbedded ? "gap-3" : "gap-2"

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      onSend()
    }
  }

  return (
    <div className={`${padding} bg-gradient-to-t from-muted/20 to-transparent flex-shrink-0`}>
      <div className={`flex ${gap}`}>
        <div className="flex-1 relative">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="min-h-[44px] max-h-32 resize-none pr-12 text-sm"
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />
          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
            {input.length}/2000
          </div>
        </div>
        <Button
          onClick={onSend}
          disabled={!input.trim() || isLoading || !apiKey}
          size="icon"
          className="h-11 w-11 rounded-xl"
        >
          {isLoading ? (
            <RotateCcw className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </div>
      <StatusIndicator apiKey={apiKey} isEmbedded={isEmbedded} />
    </div>
  )
}

// 设置对话框组件
const SettingsDialog: React.FC<SettingsDialogProps> = ({
  open,
  onOpenChange,
  apiKey,
  setApiKey,
  baseUrl,
  setBaseUrl,
  onSave
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            API Settings
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="api-key" className="text-sm font-medium">API Key</Label>
            <PasswordInput
              id="api-key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your API key"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="base-url" className="text-sm font-medium">Base URL</Label>
            <Input
              id="base-url"
              type="url"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="https://api.example.com/v1/chat/completions"
              className="mt-1"
            />
          </div>
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={onSave}>
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// 头部组件
const ChatHeader: React.FC<ChatHeaderProps> = ({
  currentModel,
  selectedModel,
  setSelectedModel,
  onSettingsClick,
  onClose,
  isEmbedded
}) => {
  return (
    <CardHeader className={cn(
      "flex flex-row items-center justify-between space-y-0 pb-3 border-b border-border/50 flex-shrink-0",
      isEmbedded
        ? ""
        : "bg-gradient-to-r from-background/80 to-muted/20"
    )}>
      <div className="flex items-center gap-3">
        <div className="relative">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
            <Bot className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
        </div>
        <div className="flex flex-col">
          <CardTitle className="text-base font-semibold flex items-center gap-2">
            AI Assistant
            <Badge variant="secondary" className="text-xs px-2 py-0.5">
              {currentModel?.label || 'Unknown'}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              {currentModel?.icon && (
                <currentModel.icon className="h-3 w-3" />
              )}
              <span>{currentModel?.description || 'AI Model'}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-1">
        <Select value={selectedModel} onValueChange={setSelectedModel}>
          <SelectTrigger className={cn(
            "text-xs px-3 border-border/50",
            isEmbedded ? "h-8 w-40" : "h-7 w-32"
          )}>
            <SelectValue placeholder="Select model" />
          </SelectTrigger>
          <SelectContent>
            {AVAILABLE_MODELS.map((model) => (
              <SelectItem key={model.value} value={model.value} className="text-xs">
                <div className="flex items-center gap-2">
                  <div className={cn("w-2 h-2 rounded-full", model.color)} />
                  <model.icon className="h-3 w-3" />
                  <span>{model.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "hover:bg-muted/50",
            isEmbedded ? "h-8 w-8" : "h-7 w-7"
          )}
          onClick={onSettingsClick}
        >
          <Settings className="h-4 w-4" />
        </Button>
        {onClose && (
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 hover:bg-muted/50"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </CardHeader>
  )
}

export function AIChat({ className, embedded = false, height = "500px" }: AIChatProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [apiKey, setApiKey] = useState('')
  const [baseUrl, setBaseUrl] = useState('https://b4u.qzz.io/v1/chat/completions')
  const [selectedModel, setSelectedModel] = useState('gpt-4.1-mini')
  const [isOpen, setIsOpen] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('ai-assistant-api-key')
    const savedBaseUrl = localStorage.getItem('ai-assistant-base-url')
    const savedModel = localStorage.getItem('ai-assistant-model')

    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
    if (savedBaseUrl) {
      setBaseUrl(savedBaseUrl)
    }
    if (savedModel) {
      setSelectedModel(savedModel)
    }
  }, [])

  // Auto scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const saveSettings = () => {
    localStorage.setItem('ai-assistant-api-key', apiKey)
    localStorage.setItem('ai-assistant-base-url', baseUrl)
    localStorage.setItem('ai-assistant-model', selectedModel)
    setShowSettings(false)
  }

  const sendMessage = async () => {
    if (!input.trim() || !apiKey) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant. Provide clear, concise, and helpful responses.'
            },
            ...messages.map(msg => ({
              role: msg.role,
              content: msg.content
            })),
            {
              role: 'user',
              content: input.trim()
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.choices[0]?.message?.content || 'Sorry, I could not generate a response.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, there was an error processing your request. Please check your API key and try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }



  const clearChat = () => {
    setMessages([])
  }

  // 嵌入式模式：直接显示聊天界面
  if (embedded) {
    const currentModel = AVAILABLE_MODELS.find(m => m.value === selectedModel)

    return (
      <Card className={cn(
        "w-full shadow-lg border-0 bg-gradient-to-br from-background to-muted/20",
        "backdrop-blur-sm border border-border/50",
        className
      )} style={{ height, display: 'flex', flexDirection: 'column' }}>
        <ChatHeader
          currentModel={currentModel}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          onSettingsClick={() => setShowSettings(true)}
          isEmbedded={true}
        />
        <SettingsDialog
          open={showSettings}
          onOpenChange={setShowSettings}
          apiKey={apiKey}
          setApiKey={setApiKey}
          baseUrl={baseUrl}
          setBaseUrl={setBaseUrl}
          onSave={saveSettings}
        />
        <CardContent className="flex flex-col flex-1 min-h-0">
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full px-4 py-4">
              {messages.length === 0 ? (
                <EmptyState apiKey={apiKey} isEmbedded={true} />
              ) : (
                <MessageList
                  messages={messages}
                  isLoading={isLoading}
                  messagesEndRef={messagesEndRef}
                  isEmbedded={true}
                />
              )}
            </ScrollArea>
          </div>
          <Separator className="bg-border/50" />
          <ChatInput
            input={input}
            setInput={setInput}
            isLoading={isLoading}
            apiKey={apiKey}
            onSend={sendMessage}
            isEmbedded={true}
          />
          {messages.length > 0 && (
            <div className="px-4 pb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearChat}
                className="text-xs h-8 px-3 hover:bg-destructive/10 hover:text-destructive transition-colors"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear Chat
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // 浮动模式：显示浮动按钮或聊天窗口
  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={cn(
          "fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-xl z-50",
          "bg-gradient-to-br from-primary to-primary/90 hover:from-primary/90 hover:to-primary",
          "border-2 border-primary-foreground/20 backdrop-blur-sm",
          "transition-all duration-300 hover:scale-110 hover:shadow-2xl",
          "animate-in fade-in-50 slide-in-from-bottom-4 duration-500"
        )}
        size="icon"
      >
        <MessageCircle className="h-6 w-6 text-primary-foreground" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-pulse" />
      </Button>
    )
  }

  const currentModel = AVAILABLE_MODELS.find(m => m.value === selectedModel)

  return (
    <Card className={cn(
      "fixed bottom-6 right-6 w-96 h-[600px] shadow-2xl z-50 border-0",
      "bg-gradient-to-br from-background to-muted/20 backdrop-blur-xl",
      "border border-border/50 rounded-2xl overflow-hidden",
      "animate-in fade-in-50 slide-in-from-bottom-8 duration-500",
      "flex flex-col"
    )}>
      <ChatHeader
        currentModel={currentModel}
        selectedModel={selectedModel}
        setSelectedModel={setSelectedModel}
        onSettingsClick={() => setShowSettings(true)}
        onClose={() => setIsOpen(false)}
        isEmbedded={false}
      />
      <SettingsDialog
        open={showSettings}
        onOpenChange={setShowSettings}
        apiKey={apiKey}
        setApiKey={setApiKey}
        baseUrl={baseUrl}
        setBaseUrl={setBaseUrl}
        onSave={saveSettings}
      />
      <CardContent className="p-0 flex flex-col h-[calc(100%-80px)]">
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-3 py-4">
          {messages.length === 0 ? (
            <EmptyState apiKey={apiKey} isEmbedded={false} />
          ) : (
            <MessageList
              messages={messages}
              isLoading={isLoading}
              messagesEndRef={messagesEndRef}
              isEmbedded={false}
            />
          )}
          </ScrollArea>
        </div>
        <Separator className="bg-border/50" />
        <ChatInput
          input={input}
          setInput={setInput}
          isLoading={isLoading}
          apiKey={apiKey}
          onSend={sendMessage}
          isEmbedded={false}
        />
        {messages.length > 0 && (
          <div className="px-3 pb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearChat}
              className="text-xs h-7 px-2 hover:bg-destructive/10 hover:text-destructive transition-colors"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Clear
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
