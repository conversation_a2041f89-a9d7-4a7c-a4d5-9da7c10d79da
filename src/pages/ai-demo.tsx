import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AIChat } from '@/components/ai-assistant/ai-chat'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { <PERSON><PERSON>, MessageSquare, Sparkles } from 'lucide-react'

export default function AIDemoPage() {
  const [showFloating, setShowFloating] = useState(false)

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
            <Bot className="h-6 w-6 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              AI Assistant Demo
            </h1>
            <p className="text-muted-foreground">
              Experience our enhanced AI chat interface with modern design
            </p>
          </div>
        </div>
        
        <div className="flex items-center justify-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <Sparkles className="h-3 w-3" />
            Enhanced UI
          </Badge>
          <Badge variant="outline" className="gap-1">
            <MessageSquare className="h-3 w-3" />
            Real-time Chat
          </Badge>
        </div>
      </div>

      {/* Demo Tabs */}
      <Tabs defaultValue="embedded" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="embedded">Embedded Mode</TabsTrigger>
          <TabsTrigger value="floating">Floating Mode</TabsTrigger>
        </TabsList>
        
        <TabsContent value="embedded" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Embedded AI Chat
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                This mode integrates the AI chat directly into your page layout.
                Perfect for dedicated chat pages or dashboard widgets.
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Small embedded chat */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Compact Size (400px)</h3>
                  <AIChat embedded height="400px" className="border-2" />
                </div>
                
                {/* Large embedded chat */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Standard Size (600px)</h3>
                  <AIChat embedded height="600px" className="border-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="floating" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Floating AI Chat
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                This mode provides a floating chat button that opens a chat window.
                Great for providing AI assistance across your entire application.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button 
                  onClick={() => setShowFloating(!showFloating)}
                  variant={showFloating ? "destructive" : "default"}
                >
                  {showFloating ? "Hide" : "Show"} Floating Chat
                </Button>
                <p className="text-sm text-muted-foreground">
                  {showFloating 
                    ? "The floating chat is now active in the bottom-right corner" 
                    : "Click to activate the floating chat interface"
                  }
                </p>
              </div>
              
              {showFloating && (
                <div className="p-4 bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/20">
                  <p className="text-sm text-muted-foreground text-center">
                    ✨ Look at the bottom-right corner of your screen!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>✨ Enhanced Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium">🎨 Modern Design</h3>
              <p className="text-sm text-muted-foreground">
                Beautiful gradients, smooth animations, and modern UI components
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">🤖 Smart Model Selection</h3>
              <p className="text-sm text-muted-foreground">
                Visual model picker with icons, descriptions, and status indicators
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">💬 Enhanced Chat</h3>
              <p className="text-sm text-muted-foreground">
                Improved message bubbles, timestamps, and copy functionality
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">⚡ Better Input</h3>
              <p className="text-sm text-muted-foreground">
                Multi-line support, character counter, and smart send button
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">🔧 Easy Configuration</h3>
              <p className="text-sm text-muted-foreground">
                Streamlined settings dialog with better organization
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">📱 Responsive</h3>
              <p className="text-sm text-muted-foreground">
                Works perfectly on desktop, tablet, and mobile devices
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Floating Chat Component */}
      {showFloating && <AIChat />}
    </div>
  )
}
